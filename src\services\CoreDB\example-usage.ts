import { CoreDBClient } from './CoreDBClient';
import { CoreDBWSTransport } from './CoreDBWSTransport';
import { CoreDBTCPTransport } from './CoreDBTCPTransport';

// Use with WebSocket
const wsTransport = new CoreDBWSTransport('ws://localhost:3000');
const wsClient = new CoreDBClient(wsTransport);

// Use with TCP
const tcpTransport = new CoreDBTCPTransport('localhost', 3000);
const tcpClient = new CoreDBClient(tcpTransport);